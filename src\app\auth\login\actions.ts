'use server';

import { redirect } from 'next/navigation';
import { signIn } from '@/lib/auth';
import { LoginUserSchema, type LoginUserInput } from '@/lib/validators/auth';
import { AuthError } from 'next-auth';

export async function loginUserAction(formData: FormData) {
  try {
    // Extract form data
    const rawData = {
      email: formData.get('email') as string,
      password: formData.get('password') as string,
    };

    // Validate input
    const validatedData = LoginUserSchema.parse(rawData);

    // Attempt to sign in
    await signIn('credentials', {
      email: validatedData.email,
      password: validatedData.password,
      redirect: false,
    });

    // Redirect to dashboard on success
    redirect('/dashboard');
  } catch (error) {
    console.error('Login action error:', error);
    
    if (error instanceof AuthError) {
      switch (error.type) {
        case 'CredentialsSignin':
          return {
            success: false,
            message: 'Invalid email or password',
          };
        default:
          return {
            success: false,
            message: 'An error occurred during sign in',
          };
      }
    }

    if (error instanceof Error) {
      return {
        success: false,
        message: error.message,
      };
    }

    return {
      success: false,
      message: 'An unexpected error occurred during login',
    };
  }
}

export async function loginUserActionWithReturn(input: LoginUserInput) {
  try {
    // Validate input
    const validatedData = LoginUserSchema.parse(input);

    // Attempt to sign in
    const result = await signIn('credentials', {
      email: validatedData.email,
      password: validatedData.password,
      redirect: false,
    });

    if (result?.error) {
      return {
        success: false,
        message: 'Invalid email or password',
      };
    }

    return {
      success: true,
      message: 'Login successful',
    };
  } catch (error) {
    console.error('Login action error:', error);
    
    if (error instanceof AuthError) {
      switch (error.type) {
        case 'CredentialsSignin':
          return {
            success: false,
            message: 'Invalid email or password',
          };
        default:
          return {
            success: false,
            message: 'An error occurred during sign in',
          };
      }
    }

    if (error instanceof Error) {
      return {
        success: false,
        message: error.message,
      };
    }

    return {
      success: false,
      message: 'An unexpected error occurred during login',
    };
  }
}
