const { execSync } = require('child_process');
const { Client } = require('pg');

async function initializeDatabase() {
  console.log('🔧 Initializing database...');
  
  try {
    // Extract database connection details from DATABASE_URL
    const databaseUrl = process.env.DATABASE_URL || 'postgresql://postgres:root@localhost:5432/myrealhub_platform?schema=public';
    const url = new URL(databaseUrl);
    
    const dbConfig = {
      user: url.username,
      password: url.password,
      host: url.hostname,
      port: parseInt(url.port) || 5432,
      database: 'postgres', // Connect to default postgres database first
    };
    
    const targetDatabase = url.pathname.slice(1); // Remove leading slash
    
    console.log(`📡 Connecting to PostgreSQL server at ${url.hostname}:${url.port}...`);
    
    // Connect to PostgreSQL server
    const client = new Client(dbConfig);
    await client.connect();
    
    // Check if database exists
    const result = await client.query(
      'SELECT 1 FROM pg_database WHERE datname = $1',
      [targetDatabase]
    );
    
    if (result.rows.length === 0) {
      console.log(`🗄️  Creating database '${targetDatabase}'...`);
      await client.query(`CREATE DATABASE "${targetDatabase}"`);
      console.log(`✅ Database '${targetDatabase}' created successfully!`);
    } else {
      console.log(`✅ Database '${targetDatabase}' already exists.`);
    }
    
    await client.end();
    
    // Now run Prisma commands
    console.log('🔄 Generating Prisma client...');
    execSync('npx prisma generate', { stdio: 'inherit' });
    
    console.log('📋 Pushing database schema...');
    execSync('npx prisma db push', { stdio: 'inherit' });
    
    console.log('🎉 Database initialization completed successfully!');
    
  } catch (error) {
    console.error('❌ Database initialization failed:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.error('\n💡 Make sure PostgreSQL is running on your system.');
      console.error('   You can start it with: brew services start postgresql (macOS) or systemctl start postgresql (Linux)');
    }
    
    process.exit(1);
  }
}

initializeDatabase();
