'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useRouter, useSearchParams } from 'next/navigation'
import { loginUserActionWithReturn } from './actions'

export default function LoginPage() {
	const [isLoading, setIsLoading] = useState(false)
	const [error, setError] = useState<string | null>(null)
	const [success, setSuccess] = useState<string | null>(null)
	const router = useRouter()
	const searchParams = useSearchParams()

	useEffect(() => {
		// Check for success message from registration
		const message = searchParams.get('message')
		if (message) {
			setSuccess(message)
		}
	}, [searchParams])

	async function handleSubmit(event: React.FormEvent<HTMLFormElement>) {
		event.preventDefault()
		setIsLoading(true)
		setError(null)
		setSuccess(null)

		const formData = new FormData(event.currentTarget)
		const data = {
			email: formData.get('email') as string,
			password: formData.get('password') as string
		}

		try {
			const result = await loginUserActionWithReturn(data)

			if (result.success) {
				setSuccess('Login successful! Redirecting...')
				// Redirect to dashboard
				setTimeout(() => {
					router.push('/dashboard')
				}, 1000)
			} else {
				setError(result.message)
			}
		} catch (err) {
			setError('An unexpected error occurred. Please try again.')
			console.error(err)
		} finally {
			setIsLoading(false)
		}
	}

	return (
		<div className="min-h-screen bg-background-app flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
			<div className="max-w-md w-full space-y-8">
				<div>
					<Link href="/" className="flex justify-center">
						<h1 className="text-3xl font-bold text-secondary">MyRealHub</h1>
					</Link>
					<h2 className="mt-6 text-center text-3xl font-bold text-secondary">Sign in to your account</h2>
					<p className="mt-2 text-center text-sm text-text-soft">
						Or{' '}
						<Link href="/auth/register" className="font-medium text-primary hover:text-primary-hover">
							create a new account
						</Link>
					</p>
				</div>

				<div className="card">
					{error && (
						<div className="mb-4 p-4 bg-red-50 border border-red-200 rounded">
							<p className="text-sm text-red-600">{error}</p>
						</div>
					)}

					{success && (
						<div className="mb-4 p-4 bg-green-50 border border-green-200 rounded">
							<p className="text-sm text-green-600">{success}</p>
						</div>
					)}

					<form onSubmit={handleSubmit} className="space-y-6">
						<div>
							<label htmlFor="email" className="block text-sm font-medium text-text-primary">
								Email Address
							</label>
							<input
								id="email"
								name="email"
								type="email"
								required
								className="input-field mt-1"
								placeholder="Enter your email"
							/>
						</div>

						<div>
							<label htmlFor="password" className="block text-sm font-medium text-text-primary">
								Password
							</label>
							<input
								id="password"
								name="password"
								type="password"
								required
								className="input-field mt-1"
								placeholder="Enter your password"
							/>
						</div>

						<div className="flex items-center justify-between">
							<div className="flex items-center">
								<input
									id="remember-me"
									name="remember-me"
									type="checkbox"
									className="h-4 w-4 text-primary focus:ring-primary border-border-subtle rounded"
								/>
								<label htmlFor="remember-me" className="ml-2 block text-sm text-text-soft">
									Remember me
								</label>
							</div>

							<div className="text-sm">
								<Link href="/auth/forgot-password" className="font-medium text-primary hover:text-primary-hover">
									Forgot your password?
								</Link>
							</div>
						</div>

						<div>
							<button
								type="submit"
								disabled={isLoading}
								className="w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
							>
								{isLoading ? 'Signing in...' : 'Sign in'}
							</button>
						</div>
					</form>
				</div>

				<div className="text-center">
					<p className="text-xs text-text-soft">
						By signing in, you agree to our{' '}
						<Link href="/terms" className="text-primary hover:text-primary-hover">
							Terms of Service
						</Link>{' '}
						and{' '}
						<Link href="/privacy" className="text-primary hover:text-primary-hover">
							Privacy Policy
						</Link>
					</p>
				</div>
			</div>
		</div>
	)
}
