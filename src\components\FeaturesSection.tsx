import FeatureCard from './FeatureCard'

interface Feature {
	icon: React.ReactNode
	title: string
	description: string
}

interface FeaturesSectionProps {
	id?: string
	title: string
	description: string
	features: Feature[]
}

export default function FeaturesSection({ id, title, description, features }: FeaturesSectionProps) {
	return (
		<section id={id} className="py-20">
			<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
				<div className="text-center mb-16">
					<h2 className="text-4xl font-bold text-secondary mb-4">{title}</h2>
					<p className="text-xl text-text-soft max-w-2xl mx-auto">
						{description}
					</p>
				</div>

				<div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
					{features.map((feature, index) => (
						<FeatureCard
							key={index}
							icon={feature.icon}
							title={feature.title}
							description={feature.description}
						/>
					))}
				</div>
			</div>
		</section>
	)
}
