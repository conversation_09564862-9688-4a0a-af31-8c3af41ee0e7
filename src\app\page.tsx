import Header from '@/components/Header'
import Footer from '@/components/Footer'
import HeroSection from '@/components/HeroSection'
import FeaturesSection from '@/components/FeaturesSection'
import CTASection from '@/components/CTASection'

export default function Home() {
	const heroButtons = [
		{ text: 'Start Your Journey', href: '/auth/register', variant: 'primary' as const },
		{ text: 'Explore Features', href: '#features', variant: 'secondary' as const }
	]

	const features = [
		{
			icon: (
				<svg className="w-6 h-6 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path
						strokeLinecap="round"
						strokeLinejoin="round"
						strokeWidth={2}
						d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
					/>
				</svg>
			),
			title: 'Smart Recommendations',
			description:
				'Our AI analyzes your preferences, budget, and lifestyle to suggest properties that perfectly match your needs.'
		},
		{
			icon: (
				<svg className="w-6 h-6 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path
						strokeLinecap="round"
						strokeLinejoin="round"
						strokeWidth={2}
						d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
					/>
				</svg>
			),
			title: 'Real-Time Market Analysis',
			description:
				'Get instant insights into market trends, property values, and investment opportunities with our advanced analytics.'
		},
		{
			icon: (
				<svg className="w-6 h-6 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path
						strokeLinecap="round"
						strokeLinejoin="round"
						strokeWidth={2}
						d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
					/>
				</svg>
			),
			title: 'Intelligent Search',
			description:
				"Search using natural language and let our AI understand exactly what you're looking for in your next property."
		},
		{
			icon: (
				<svg className="w-6 h-6 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path
						strokeLinecap="round"
						strokeLinejoin="round"
						strokeWidth={2}
						d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
					/>
				</svg>
			),
			title: 'Price Prediction',
			description:
				'Leverage machine learning models to predict future property values and make informed investment decisions.'
		},
		{
			icon: (
				<svg className="w-6 h-6 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path
						strokeLinecap="round"
						strokeLinejoin="round"
						strokeWidth={2}
						d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
					/>
				</svg>
			),
			title: 'AI-Enhanced Virtual Tours',
			description:
				'Experience properties like never before with AI-powered virtual tours and augmented reality features.'
		},
		{
			icon: (
				<svg className="w-6 h-6 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
				</svg>
			),
			title: 'Investment Insights',
			description:
				'Get comprehensive investment analysis including ROI predictions, rental yield estimates, and market growth forecasts.'
		}
	]

	return (
		<div className="min-h-screen bg-background-app">
			<Header />

			<HeroSection
				title={
					<>
						The Future of Real Estate is <span className="text-primary">AI-Powered</span>
					</>
				}
				description="Discover your perfect property with intelligent recommendations, real-time market analysis, and personalized insights powered by cutting-edge artificial intelligence."
				buttons={heroButtons}
			/>

			{/* Features Section */}
			<section id="features" className="py-20">
				<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="text-center mb-16">
						<h2 className="text-4xl font-bold text-secondary mb-4">AI-Powered Real Estate Solutions</h2>
						<p className="text-xl text-text-soft max-w-2xl mx-auto">
							Experience the next generation of property discovery and investment with our intelligent platform.
						</p>
					</div>

					<div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
						{/* Smart Property Recommendations */}
						<div className="card">
							<div className="w-12 h-12 bg-primary rounded-sm flex items-center justify-center mb-4">
								<svg className="w-6 h-6 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path
										strokeLinecap="round"
										strokeLinejoin="round"
										strokeWidth={2}
										d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
									/>
								</svg>
							</div>
							<h3 className="text-xl font-semibold text-secondary mb-3">Smart Recommendations</h3>
							<p className="text-text-soft">
								Our AI analyzes your preferences, budget, and lifestyle to suggest properties that perfectly match your
								needs.
							</p>
						</div>

						{/* Market Analysis */}
						<div className="card">
							<div className="w-12 h-12 bg-primary rounded-sm flex items-center justify-center mb-4">
								<svg className="w-6 h-6 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path
										strokeLinecap="round"
										strokeLinejoin="round"
										strokeWidth={2}
										d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
									/>
								</svg>
							</div>
							<h3 className="text-xl font-semibold text-secondary mb-3">Real-Time Market Analysis</h3>
							<p className="text-text-soft">
								Get instant insights into market trends, property values, and investment opportunities with our advanced
								analytics.
							</p>
						</div>

						{/* Intelligent Search */}
						<div className="card">
							<div className="w-12 h-12 bg-primary rounded-sm flex items-center justify-center mb-4">
								<svg className="w-6 h-6 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path
										strokeLinecap="round"
										strokeLinejoin="round"
										strokeWidth={2}
										d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
									/>
								</svg>
							</div>
							<h3 className="text-xl font-semibold text-secondary mb-3">Intelligent Search</h3>
							<p className="text-text-soft">
								Search using natural language and let our AI understand exactly what you&apos;re looking for in your
								next property.
							</p>
						</div>

						{/* Price Prediction */}
						<div className="card">
							<div className="w-12 h-12 bg-primary rounded-sm flex items-center justify-center mb-4">
								<svg className="w-6 h-6 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path
										strokeLinecap="round"
										strokeLinejoin="round"
										strokeWidth={2}
										d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
									/>
								</svg>
							</div>
							<h3 className="text-xl font-semibold text-secondary mb-3">Price Prediction</h3>
							<p className="text-text-soft">
								Leverage machine learning models to predict future property values and make informed investment
								decisions.
							</p>
						</div>

						{/* Virtual Tours */}
						<div className="card">
							<div className="w-12 h-12 bg-primary rounded-sm flex items-center justify-center mb-4">
								<svg className="w-6 h-6 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path
										strokeLinecap="round"
										strokeLinejoin="round"
										strokeWidth={2}
										d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
									/>
								</svg>
							</div>
							<h3 className="text-xl font-semibold text-secondary mb-3">AI-Enhanced Virtual Tours</h3>
							<p className="text-text-soft">
								Experience properties like never before with AI-powered virtual tours and augmented reality features.
							</p>
						</div>

						{/* Investment Insights */}
						<div className="card">
							<div className="w-12 h-12 bg-primary rounded-sm flex items-center justify-center mb-4">
								<svg className="w-6 h-6 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path
										strokeLinecap="round"
										strokeLinejoin="round"
										strokeWidth={2}
										d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"
									/>
								</svg>
							</div>
							<h3 className="text-xl font-semibold text-secondary mb-3">Investment Insights</h3>
							<p className="text-text-soft">
								Get comprehensive investment analysis including ROI predictions, rental yield estimates, and market
								growth forecasts.
							</p>
						</div>
					</div>
				</div>
			</section>

			{/* CTA Section */}
			<section className="bg-secondary py-20">
				<div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
					<h2 className="text-4xl font-bold text-white mb-6">Ready to Transform Your Real Estate Experience?</h2>
					<p className="text-xl text-gray-300 mb-8">
						Join thousands of users who are already discovering their perfect properties with AI-powered insights.
					</p>
					<Link href="/auth/register" className="btn-primary text-lg px-8 py-4">
						Get Started Today
					</Link>
				</div>
			</section>

			<Footer />
		</div>
	)
}
