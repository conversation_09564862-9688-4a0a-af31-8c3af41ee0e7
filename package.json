{"name": "myrealhub-platform", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:init": "node scripts/init-db.js", "db:test": "node scripts/test-db.js", "db:setup": "npx prisma generate && npx prisma db push", "db:reset": "npx prisma db push --force-reset", "db:studio": "npx prisma studio"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@prisma/client": "^6.13.0", "@types/bcryptjs": "^2.4.6", "bcryptjs": "^3.0.2", "next": "15.4.5", "next-auth": "^5.0.0-beta.29", "pg": "^8.16.3", "prisma": "^6.13.0", "react": "19.1.0", "react-dom": "19.1.0", "zod": "^4.0.14"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.5", "tailwindcss": "^4", "typescript": "^5"}}