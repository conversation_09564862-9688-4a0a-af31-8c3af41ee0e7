import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { auth } from '@/lib/auth';

// Define protected routes and their required roles
const protectedRoutes = {
  '/dashboard': ['USER', 'BUY<PERSON>', 'AGENT', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', 'ADMIN'],
  '/admin': ['ADMIN'],
  '/agent': ['AGENT', 'BROKER', 'ADMIN'],
  '/broker': ['BR<PERSON><PERSON>', 'ADMIN'],
  '/developer': ['DEVELOPER', 'ADMIN'],
  '/api/protected': ['USER', 'BUY<PERSON>', 'AGENT', 'BROK<PERSON>', 'DEVE<PERSON>OP<PERSON>', 'ADMI<PERSON>'],
  '/api/admin': ['ADMIN'],
  '/api/agent': ['AGENT', '<PERSON><PERSON><PERSON>', 'ADMIN'],
  '/api/broker': ['<PERSON><PERSON><PERSON>', 'ADMIN'],
  '/api/developer': ['<PERSON><PERSON><PERSON><PERSON><PERSON>', 'ADMI<PERSON>'],
};

// Public routes that don't require authentication
const publicRoutes = [
  '/',
  '/auth/login',
  '/auth/register',
  '/about',
  '/contact',
  '/privacy',
  '/terms',
  '/api/auth',
  '/api/auth/register',
  '/api/auth/login',
];

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Check if the route is public
  const isPublicRoute = publicRoutes.some(route => 
    pathname === route || pathname.startsWith(route + '/')
  );

  if (isPublicRoute) {
    return NextResponse.next();
  }

  // Check if the route is protected
  const protectedRoute = Object.keys(protectedRoutes).find(route =>
    pathname === route || pathname.startsWith(route + '/')
  );

  if (!protectedRoute) {
    return NextResponse.next();
  }

  // Get the session
  const session = await auth();

  if (!session?.user) {
    // Redirect to login for web routes
    if (!pathname.startsWith('/api/')) {
      const loginUrl = new URL('/auth/login', request.url);
      loginUrl.searchParams.set('callbackUrl', pathname);
      return NextResponse.redirect(loginUrl);
    }
    
    // Return 401 for API routes
    return NextResponse.json(
      { success: false, message: 'Authentication required' },
      { status: 401 }
    );
  }

  // Check if user has required role
  const requiredRoles = protectedRoutes[protectedRoute as keyof typeof protectedRoutes];
  const userRole = session.user.role;

  if (!requiredRoles.includes(userRole)) {
    // Redirect to dashboard for web routes
    if (!pathname.startsWith('/api/')) {
      return NextResponse.redirect(new URL('/dashboard', request.url));
    }
    
    // Return 403 for API routes
    return NextResponse.json(
      { success: false, message: 'Insufficient permissions' },
      { status: 403 }
    );
  }

  // Check if user account is active
  if (!session.user.isActive) {
    // Redirect to login for web routes
    if (!pathname.startsWith('/api/')) {
      const loginUrl = new URL('/auth/login', request.url);
      loginUrl.searchParams.set('error', 'account-deactivated');
      return NextResponse.redirect(loginUrl);
    }
    
    // Return 403 for API routes
    return NextResponse.json(
      { success: false, message: 'Account has been deactivated' },
      { status: 403 }
    );
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};
