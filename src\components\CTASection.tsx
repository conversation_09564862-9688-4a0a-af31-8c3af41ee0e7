import Link from 'next/link'

interface CTASectionProps {
	title: string
	description: string
	buttonText: string
	buttonHref: string
	backgroundColor?: string
	titleColor?: string
	descriptionColor?: string
}

export default function CTASection({
	title,
	description,
	buttonText,
	buttonHref,
	backgroundColor = 'bg-secondary',
	titleColor = 'text-white',
	descriptionColor = 'text-gray-300'
}: CTASectionProps) {
	return (
		<section className={`${backgroundColor} py-20`}>
			<div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
				<h2 className={`text-4xl font-bold ${titleColor} mb-6`}>{title}</h2>
				<p className={`text-xl ${descriptionColor} mb-8`}>
					{description}
				</p>
				<Link href={buttonHref} className="btn-primary text-lg px-8 py-4">
					{buttonText}
				</Link>
			</div>
		</section>
	)
}
