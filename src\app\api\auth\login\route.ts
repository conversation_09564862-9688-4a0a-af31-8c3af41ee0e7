import { NextRequest, NextResponse } from 'next/server';
import { authenticateUser } from '@/lib/api/auth';
import { LoginUserSchema } from '@/lib/validators/auth';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate input
    const validatedData = LoginUserSchema.parse(body);
    
    // Authenticate user
    const result = await authenticateUser(validatedData);
    
    if (!result.success) {
      return NextResponse.json(result, { status: 401 });
    }
    
    return NextResponse.json(result, { status: 200 });
  } catch (error) {
    console.error('Login API error:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        {
          success: false,
          message: error.message,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      {
        success: false,
        message: 'An unexpected error occurred during login',
      },
      { status: 500 }
    );
  }
}
