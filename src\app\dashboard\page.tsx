import { auth } from '@/lib/auth';
import { redirect } from 'next/navigation';
import Link from 'next/link';
import { signOut } from '@/lib/auth';

async function signOutAction() {
  'use server';
  await signOut();
}

export default async function DashboardPage() {
  const session = await auth();

  if (!session?.user) {
    redirect('/auth/login');
  }

  const { user } = session;

  return (
    <div className="min-h-screen bg-background-app">
      {/* Navigation */}
      <nav className="bg-card border-b border-border-subtle">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/" className="text-2xl font-bold text-secondary">
                MyRealHub
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-text-soft">
                Welcome, {user.firstName} {user.lastName}
              </span>
              <span className="px-2 py-1 bg-primary text-secondary text-xs font-medium rounded-full">
                {user.role}
              </span>
              <form action={signOutAction}>
                <button type="submit" className="btn-secondary">
                  Sign Out
                </button>
              </form>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-secondary">Dashboard</h1>
            <p className="mt-2 text-text-soft">
              Welcome to your AI-powered real estate platform
            </p>
          </div>

          {/* User Info Card */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <div className="card">
              <h3 className="text-lg font-semibold text-secondary mb-4">Profile Information</h3>
              <div className="space-y-2">
                <p><span className="font-medium">Name:</span> {user.firstName} {user.lastName}</p>
                <p><span className="font-medium">Email:</span> {user.email}</p>
                <p><span className="font-medium">Phone:</span> {user.phone || 'Not provided'}</p>
                <p><span className="font-medium">Role:</span> {user.role}</p>
                <p><span className="font-medium">Status:</span> 
                  <span className={`ml-1 ${user.isActive ? 'text-green-600' : 'text-red-600'}`}>
                    {user.isActive ? 'Active' : 'Inactive'}
                  </span>
                </p>
              </div>
            </div>

            <div className="card">
              <h3 className="text-lg font-semibold text-secondary mb-4">Quick Actions</h3>
              <div className="space-y-3">
                <button className="w-full btn-primary text-left">
                  🔍 Search Properties
                </button>
                <button className="w-full btn-secondary text-left">
                  📊 Market Analysis
                </button>
                <button className="w-full btn-secondary text-left">
                  💡 AI Recommendations
                </button>
              </div>
            </div>

            <div className="card">
              <h3 className="text-lg font-semibold text-secondary mb-4">AI Insights</h3>
              <div className="space-y-3">
                <div className="p-3 bg-primary/10 rounded-lg">
                  <p className="text-sm text-secondary">
                    🏠 <strong>3 new properties</strong> match your preferences
                  </p>
                </div>
                <div className="p-3 bg-primary/10 rounded-lg">
                  <p className="text-sm text-secondary">
                    📈 Market trends suggest <strong>5% growth</strong> in your area
                  </p>
                </div>
                <div className="p-3 bg-primary/10 rounded-lg">
                  <p className="text-sm text-secondary">
                    💰 Your budget can get <strong>15% more</strong> in nearby areas
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Role-specific Features */}
          {user.role === 'ADMIN' && (
            <div className="card mb-6">
              <h3 className="text-lg font-semibold text-secondary mb-4">Admin Features</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <button className="btn-secondary">User Management</button>
                <button className="btn-secondary">System Settings</button>
                <button className="btn-secondary">Analytics</button>
                <button className="btn-secondary">Reports</button>
              </div>
            </div>
          )}

          {(user.role === 'AGENT' || user.role === 'BROKER') && (
            <div className="card mb-6">
              <h3 className="text-lg font-semibold text-secondary mb-4">Agent Tools</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <button className="btn-secondary">Client Management</button>
                <button className="btn-secondary">Listing Management</button>
                <button className="btn-secondary">Lead Generation</button>
                <button className="btn-secondary">Commission Tracker</button>
              </div>
            </div>
          )}

          {user.role === 'DEVELOPER' && (
            <div className="card mb-6">
              <h3 className="text-lg font-semibold text-secondary mb-4">Developer Tools</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <button className="btn-secondary">Project Management</button>
                <button className="btn-secondary">Site Analysis</button>
                <button className="btn-secondary">Market Research</button>
                <button className="btn-secondary">Investment Calculator</button>
              </div>
            </div>
          )}

          {/* Recent Activity */}
          <div className="card">
            <h3 className="text-lg font-semibold text-secondary mb-4">Recent Activity</h3>
            <div className="space-y-4">
              <div className="flex items-center space-x-3 p-3 bg-background-app rounded-lg">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                <p className="text-sm text-text-soft">Account created successfully</p>
                <span className="text-xs text-text-soft ml-auto">Just now</span>
              </div>
              <div className="flex items-center space-x-3 p-3 bg-background-app rounded-lg">
                <div className="w-2 h-2 bg-secondary-muted rounded-full"></div>
                <p className="text-sm text-text-soft">Welcome to MyRealHub platform</p>
                <span className="text-xs text-text-soft ml-auto">Just now</span>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
