'use server';

import { redirect } from 'next/navigation';
import { registerUser } from '@/lib/api/auth';
import { RegisterUserSchema, type RegisterUserInput } from '@/lib/validators/auth';

export async function registerUserAction(formData: FormData) {
  try {
    // Extract form data
    const rawData = {
      email: formData.get('email') as string,
      password: formData.get('password') as string,
      confirmPassword: formData.get('confirmPassword') as string,
      firstName: formData.get('firstName') as string,
      lastName: formData.get('lastName') as string,
      phone: formData.get('phone') as string,
      role: (formData.get('role') as string) || 'USER',
    };

    // Validate input
    const validatedData = RegisterUserSchema.parse(rawData);

    // Register user
    const result = await registerUser(validatedData);

    if (!result.success) {
      return {
        success: false,
        message: result.message,
        errors: result.errors,
      };
    }

    // Redirect to login page on success
    redirect('/auth/login?message=Registration successful! Please sign in.');
  } catch (error) {
    console.error('Registration action error:', error);
    
    if (error instanceof Error) {
      return {
        success: false,
        message: error.message,
      };
    }

    return {
      success: false,
      message: 'An unexpected error occurred during registration',
    };
  }
}

export async function registerUserActionWithReturn(input: RegisterUserInput) {
  try {
    // Validate input
    const validatedData = RegisterUserSchema.parse(input);

    // Register user
    const result = await registerUser(validatedData);

    return result;
  } catch (error) {
    console.error('Registration action error:', error);
    
    if (error instanceof Error) {
      return {
        success: false,
        message: error.message,
      };
    }

    return {
      success: false,
      message: 'An unexpected error occurred during registration',
    };
  }
}
