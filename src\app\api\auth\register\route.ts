import { NextRequest, NextResponse } from 'next/server';
import { registerUser } from '@/lib/api/auth';
import { RegisterUserSchema } from '@/lib/validators/auth';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate input
    const validatedData = RegisterUserSchema.parse(body);
    
    // Register user
    const result = await registerUser(validatedData);
    
    if (!result.success) {
      return NextResponse.json(result, { status: 400 });
    }
    
    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    console.error('Registration API error:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        {
          success: false,
          message: error.message,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      {
        success: false,
        message: 'An unexpected error occurred during registration',
      },
      { status: 500 }
    );
  }
}
