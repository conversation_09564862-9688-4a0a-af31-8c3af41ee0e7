import Link from 'next/link'

interface HeroButton {
	text: string
	href: string
	variant: 'primary' | 'secondary'
}

interface HeroSectionProps {
	title: React.ReactNode
	description: string
	buttons: HeroButton[]
}

export default function HeroSection({ title, description, buttons }: HeroSectionProps) {
	return (
		<section className="relative bg-gradient-to-br from-primary/10 to-secondary/5 py-20">
			<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
				<div className="text-center">
					<h1 className="text-5xl md:text-6xl font-bold text-secondary mb-6">
						{title}
					</h1>
					<p className="text-xl text-text-soft mb-8 max-w-3xl mx-auto">
						{description}
					</p>
					<div className="flex flex-col sm:flex-row gap-4 justify-center">
						{buttons.map((button, index) => (
							<Link
								key={index}
								href={button.href}
								className={`${
									button.variant === 'primary' ? 'btn-primary' : 'btn-secondary'
								} text-lg px-8 py-4`}
							>
								{button.text}
							</Link>
						))}
					</div>
				</div>
			</div>
		</section>
	)
}
