interface FeatureCardProps {
	icon: React.ReactNode
	title: string
	description: string
}

export default function FeatureCard({ icon, title, description }: FeatureCardProps) {
	return (
		<div className="card">
			<div className="w-12 h-12 bg-primary rounded flex items-center justify-center mb-4">{icon}</div>
			<h3 className="text-xl font-semibold text-secondary mb-3">{title}</h3>
			<p className="text-text-soft">{description}</p>
		</div>
	)
}
