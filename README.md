# MyRealHub - AI-Powered Real Estate Platform

A modern, AI-powered real estate platform built with Next.js 15, featuring intelligent property recommendations, market analysis, and role-based access control.

## 🚀 Features

### AI-Powered Capabilities

- **Smart Property Recommendations**: AI analyzes user preferences, budget, and lifestyle
- **Real-Time Market Analysis**: Advanced analytics for market trends and property values
- **Intelligent Search**: Natural language property search powered by AI
- **Price Prediction**: Machine learning models for future property value forecasting
- **AI-Enhanced Virtual Tours**: Immersive property experiences with AR features
- **Investment Insights**: Comprehensive ROI predictions and market growth forecasts

### Authentication & Security

- **NextAuth Integration**: Secure authentication with credentials provider
- **Role-Based Access Control**: Support for User, Buyer, Agent, Broker, Developer, and Admin roles
- **Dual Architecture**: Both Server Actions (web) and REST API (mobile) support
- **Input Validation**: Comprehensive Zod schema validation
- **Password Security**: Bcrypt hashing with strong password requirements

### Technical Stack

- **Frontend**: Next.js 15+ with App Router
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: NextAuth with credentials provider
- **Validation**: Zod schemas for type-safe validation
- **Styling**: Tailwind CSS with custom brand colors
- **TypeScript**: Full type safety throughout the application

## 🎨 Brand Colors

- **Primary**: #B8E078 (Light Green)
- **Primary Hover**: #9AC55F (Darker Green)
- **Secondary**: #0A2238 (Dark Blue)
- **Secondary Muted**: #728379 (Muted Green)
- **Text Primary**: #07111aff (Dark)
- **Text Soft**: #16222eff (Softer Dark)
- **Border Subtle**: #E1EECF (Light Green Border)
- **Background App**: #F4F7FB (Light Blue-Gray)
- **Card Background**: #FFFFFF (White)

## 🛠️ Setup Instructions

### Prerequisites

- Node.js 18+
- PostgreSQL database
- npm or yarn package manager

### Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd myrealhub-platform
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Configure environment variables**

   ```bash
   cp .env.example .env
   ```

   Update the `.env` file with your database credentials:

   ```env
   DATABASE_URL="postgresql://username:password@localhost:5432/myrealhub_platform?schema=public"
   NEXTAUTH_SECRET="your-secret-key-here"
   NEXTAUTH_URL="http://localhost:3000"
   ```

4. **Setup the database**

   ```bash
   npx prisma generate
   npx prisma db push
   ```

5. **Start the development server**

   ```bash
   npm run dev
   ```

6. **Open your browser**
   Navigate to `http://localhost:3000`

## 📁 Project Structure

```
src/
├── app/                          # Next.js App Router
│   ├── api/                      # REST API endpoints
│   │   ├── auth/                 # Authentication endpoints
│   │   └── users/                # User management endpoints
│   ├── auth/                     # Authentication pages
│   │   ├── login/                # Login page and actions
│   │   └── register/             # Registration page and actions
│   ├── dashboard/                # Protected dashboard
│   ├── globals.css               # Global styles with brand colors
│   ├── layout.tsx                # Root layout
│   └── page.tsx                  # Homepage
├── lib/                          # Shared utilities
│   ├── api/                      # Shared API logic
│   │   ├── auth.ts               # Authentication logic
│   │   └── users.ts              # User management logic
│   ├── validators/               # Zod validation schemas
│   │   └── auth.ts               # Authentication schemas
│   └── auth.ts                   # NextAuth configuration
├── middleware.ts                 # Route protection middleware
└── prisma/
    └── schema.prisma             # Database schema
```

## 🔐 User Roles

- **USER**: General platform access
- **BUYER**: Property buyer with enhanced search features
- **AGENT**: Real estate agent with client management tools
- **BROKER**: Broker with advanced agent tools
- **DEVELOPER**: Property developer with project management features
- **ADMIN**: Full platform administration access

## 🌐 API Endpoints

### Authentication

- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User authentication
- `GET/POST /api/auth/[...nextauth]` - NextAuth handlers

### User Management

- `GET /api/users` - Get all users (Admin only)
- `GET /api/users/[id]` - Get user by ID
- `PATCH /api/users/[id]` - Update user profile
- `DELETE /api/users/[id]` - Deactivate/activate user (Admin only)

## 🧪 Testing the Application

1. **Homepage**: Visit `http://localhost:3000` to see the AI-powered landing page
2. **Registration**: Click "Get Started" to create a new account
3. **Login**: Sign in with your credentials
4. **Dashboard**: Access role-specific features based on your user role
5. **API Testing**: Use tools like Postman to test REST endpoints

## 🔧 Development

### Adding New Features

1. Create Zod schemas in `src/lib/validators/`
2. Implement shared logic in `src/lib/api/`
3. Create Server Actions in `src/app/*/actions.ts`
4. Add REST endpoints in `src/app/api/*/route.ts`
5. Update middleware for route protection if needed

### Database Changes

1. Update `prisma/schema.prisma`
2. Run `npx prisma generate`
3. Run `npx prisma db push` for development
4. For production, use `npx prisma migrate dev`

## 📝 License

This project is licensed under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📞 Support

For support and questions, please contact the development team or create an issue in the repository.
