import type { Metada<PERSON> } from 'next'
import { <PERSON><PERSON><PERSON>, <PERSON>ei<PERSON>_Mono } from 'next/font/google'
import './globals.css'

const geistSans = Geist({
	variable: '--font-geist-sans',
	subsets: ['latin']
})

const geistMono = Geist_Mono({
	variable: '--font-geist-mono',
	subsets: ['latin']
})

export const metadata: Metadata = {
	title: 'MyRealHub - AI-Powered Real Estate Platform',
	description:
		'Discover your perfect property with AI-powered recommendations, market analysis, and intelligent search. The future of real estate is here.'
}

export default function RootLayout({
	children
}: Readonly<{
	children: React.ReactNode
}>) {
	return (
		<html lang="en">
			<body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>{children}</body>
		</html>
	)
}
