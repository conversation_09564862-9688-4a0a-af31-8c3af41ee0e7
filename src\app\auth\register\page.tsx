'use client'

import { useState } from 'react'
import Link from 'next/link'
import { registerUserActionWithReturn } from './actions'
import { UserRoleSchema } from '@/lib/validators/auth'

export default function RegisterPage() {
	const [isLoading, setIsLoading] = useState(false)
	const [error, setError] = useState<string | null>(null)
	const [success, setSuccess] = useState<string | null>(null)

	async function handleSubmit(event: React.FormEvent<HTMLFormElement>) {
		event.preventDefault()
		setIsLoading(true)
		setError(null)
		setSuccess(null)

		const formData = new FormData(event.currentTarget)
		const roleValue = formData.get('role') as string
		const data = {
			email: formData.get('email') as string,
			password: formData.get('password') as string,
			confirmPassword: formData.get('confirmPassword') as string,
			firstName: formData.get('firstName') as string,
			lastName: formData.get('lastName') as string,
			phone: formData.get('phone') as string,
			role: UserRoleSchema.parse(roleValue || 'USER')
		}

		try {
			const result = await registerUserActionWithReturn(data)

			if (result.success) {
				setSuccess('Registration successful! You can now sign in.')
				// Reset form
				event.currentTarget.reset()
			} else {
				setError(result.message)
			}
		} catch (err) {
			setError('An unexpected error occurred. Please try again.')
			console.error(err)
		} finally {
			setIsLoading(false)
		}
	}

	return (
		<div className="min-h-screen bg-background-app flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
			<div className="max-w-md w-full space-y-8">
				<div>
					<Link href="/" className="flex justify-center">
						<h1 className="text-3xl font-bold text-secondary">MyRealHub</h1>
					</Link>
					<h2 className="mt-6 text-center text-3xl font-bold text-secondary">Create your account</h2>
					<p className="mt-2 text-center text-sm text-text-soft">
						Or{' '}
						<Link href="/auth/login" className="font-medium text-primary hover:text-primary-hover">
							sign in to your existing account
						</Link>
					</p>
				</div>

				<div className="card">
					{error && (
						<div className="mb-4 p-4 bg-red-50 border border-red-200 rounded">
							<p className="text-sm text-red-600">{error}</p>
						</div>
					)}

					{success && (
						<div className="mb-4 p-4 bg-green-50 border border-green-200 rounded">
							<p className="text-sm text-green-600">{success}</p>
						</div>
					)}

					<form onSubmit={handleSubmit} className="space-y-6">
						<div className="grid grid-cols-2 gap-4">
							<div>
								<label htmlFor="firstName" className="block text-sm font-medium text-text-primary">
									First Name
								</label>
								<input
									id="firstName"
									name="firstName"
									type="text"
									required
									className="input-field mt-1"
									placeholder="John"
								/>
							</div>
							<div>
								<label htmlFor="lastName" className="block text-sm font-medium text-text-primary">
									Last Name
								</label>
								<input
									id="lastName"
									name="lastName"
									type="text"
									required
									className="input-field mt-1"
									placeholder="Doe"
								/>
							</div>
						</div>

						<div>
							<label htmlFor="email" className="block text-sm font-medium text-text-primary">
								Email Address
							</label>
							<input
								id="email"
								name="email"
								type="email"
								required
								className="input-field mt-1"
								placeholder="<EMAIL>"
							/>
						</div>

						<div>
							<label htmlFor="phone" className="block text-sm font-medium text-text-primary">
								Phone Number (Optional)
							</label>
							<input id="phone" name="phone" type="tel" className="input-field mt-1" placeholder="+****************" />
						</div>

						<div>
							<label htmlFor="role" className="block text-sm font-medium text-text-primary">
								I am a...
							</label>
							<select id="role" name="role" className="input-field mt-1" defaultValue="USER">
								<option value="USER">General User</option>
								<option value="BUYER">Property Buyer</option>
								<option value="AGENT">Real Estate Agent</option>
								<option value="BROKER">Broker</option>
								<option value="DEVELOPER">Developer</option>
							</select>
						</div>

						<div>
							<label htmlFor="password" className="block text-sm font-medium text-text-primary">
								Password
							</label>
							<input
								id="password"
								name="password"
								type="password"
								required
								className="input-field mt-1"
								placeholder="Enter a strong password"
							/>
							<p className="mt-1 text-xs text-text-soft">
								Must be at least 8 characters with uppercase, lowercase, and number
							</p>
						</div>

						<div>
							<label htmlFor="confirmPassword" className="block text-sm font-medium text-text-primary">
								Confirm Password
							</label>
							<input
								id="confirmPassword"
								name="confirmPassword"
								type="password"
								required
								className="input-field mt-1"
								placeholder="Confirm your password"
							/>
						</div>

						<div>
							<button
								type="submit"
								disabled={isLoading}
								className="w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
							>
								{isLoading ? 'Creating Account...' : 'Create Account'}
							</button>
						</div>
					</form>
				</div>

				<div className="text-center">
					<p className="text-xs text-text-soft">
						By creating an account, you agree to our{' '}
						<Link href="/terms" className="text-primary hover:text-primary-hover">
							Terms of Service
						</Link>{' '}
						and{' '}
						<Link href="/privacy" className="text-primary hover:text-primary-hover">
							Privacy Policy
						</Link>
					</p>
				</div>
			</div>
		</div>
	)
}
