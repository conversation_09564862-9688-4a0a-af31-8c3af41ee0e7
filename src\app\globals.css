@import "tailwindcss";

:root {
  /* Brand Colors */
  --primary: #B8E078;
  --primary-hover: #9AC55F;
  --secondary: #0A2238;
  --secondary-muted: #728379;
  --text-primary: #07111aff;
  --text-soft: #16222eff;
  --border-subtle: #E1EECF;
  --background-app: #F4F7FB;
  --card-background: #FFFFFF;

  /* Legacy variables for compatibility */
  --background: var(--background-app);
  --foreground: var(--text-primary);
}

@theme inline {
  --color-primary: var(--primary);
  --color-primary-hover: var(--primary-hover);
  --color-secondary: var(--secondary);
  --color-secondary-muted: var(--secondary-muted);
  --color-text-primary: var(--text-primary);
  --color-text-soft: var(--text-soft);
  --color-border-subtle: var(--border-subtle);
  --color-background-app: var(--background-app);
  --color-card: var(--card-background);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

body {
  background: var(--background-app);
  color: var(--text-primary);
  font-family: var(--font-geist-sans), system-ui, -apple-system, sans-serif;
  line-height: 1.6;
}

/* Custom utility classes */
.btn-primary {
  background-color: var(--primary);
  color: var(--secondary);
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  transition: all 0.2s ease;
  cursor: pointer;
}

.btn-primary:hover {
  background-color: var(--primary-hover);
  transform: translateY(-1px);
}

.btn-secondary {
  background-color: var(--secondary);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  transition: all 0.2s ease;
  cursor: pointer;
}

.btn-secondary:hover {
  background-color: var(--secondary-muted);
  transform: translateY(-1px);
}

.card {
  background: var(--card-background);
  border: 1px solid var(--border-subtle);
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.input-field {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-subtle);
  border-radius: 0.5rem;
  background: var(--card-background);
  color: var(--text-primary);
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.input-field:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(184, 224, 120, 0.1);
}
